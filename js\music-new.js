/*
音乐播放器模块
重构版本 - 使用MetingJS，更稳定的音乐播放解决方案

作者: acmakb
基于原版本修改优化
*/

// 音乐播放器配置
const musicConfig = {
    server: "netease", // 音乐服务器：netease, tencent, kugou, xiami, kuwo
    type: "playlist",  // 类型：song, playlist, album
    id: "2250011882"   // 歌单ID（使用temp.html中测试过的ID）
};

// 全局播放器实例
let globalPlayer = null;

// 初始化音乐播放器
function initMusicPlayer() {
    try {
        // 创建meting-js元素
        const metingElement = document.createElement('meting-js');
        metingElement.setAttribute('server', musicConfig.server);
        metingElement.setAttribute('type', musicConfig.type);
        metingElement.setAttribute('id', musicConfig.id);
        metingElement.setAttribute('fixed', 'false');
        metingElement.setAttribute('mini', 'false');
        metingElement.setAttribute('autoplay', 'false');
        metingElement.setAttribute('theme', '#2980b9');
        metingElement.setAttribute('loop', 'all');
        metingElement.setAttribute('order', 'random');
        metingElement.setAttribute('preload', 'auto');
        metingElement.setAttribute('volume', '0.5');
        metingElement.setAttribute('mutex', 'true');
        metingElement.setAttribute('lrctype', '3');
        metingElement.setAttribute('listfolded', 'false');
        metingElement.setAttribute('listmaxheight', '336px');
        
        // 将元素添加到aplayer容器中
        const aplayerContainer = document.getElementById('aplayer');
        if (aplayerContainer) {
            aplayerContainer.innerHTML = '';
            aplayerContainer.appendChild(metingElement);
        }

        // 等待播放器加载完成后绑定事件
        setTimeout(() => {
            bindPlayerEvents();
            setupCustomControls();
        }, 3000);

        // 播放器初始化成功提示
        setTimeout(() => {
            iziToast.success({
                timeout: 3000,
                icon: "fa-solid fa-music",
                displayMode: 'replace',
                message: '音乐播放器加载成功'
            });
        }, 4000);

    } catch (error) {
        console.error('音乐播放器初始化失败:', error);
        setTimeout(function () {
            iziToast.error({
                timeout: 8000,
                icon: "fa-solid fa-circle-exclamation",
                displayMode: 'replace',
                message: '音乐播放器加载失败'
            });
        }, 3800);
    }
}

// 绑定播放器事件
function bindPlayerEvents() {
    // 尝试获取APlayer实例
    const aplayerElement = document.querySelector('.aplayer');
    if (aplayerElement && window.aplayers && window.aplayers.length > 0) {
        globalPlayer = window.aplayers[0];
        
        // 绑定播放事件
        globalPlayer.on('play', function () {
            const music = globalPlayer.audio.name + ' - ' + globalPlayer.audio.artist;
            iziToast.info({
                timeout: 4000,
                icon: "fa-solid fa-circle-play",
                displayMode: 'replace',
                message: music
            });
            $("#play").html("<i class='fa-solid fa-pause'>");
            $("#music-name").html(music);
            
            if ($(document).width() >= 990) {
                $('.power').css("cssText", "display:none");
                $('#lrc').css("cssText", "display:block !important");
            }
            
            // 桌面通知
            if (Notification.permission === "granted") {
                new Notification('正在播放', {
                    body: music,
                    tag: 'music-notification'
                });
            }
        });

        // 绑定暂停事件
        globalPlayer.on('pause', function () {
            $("#play").html("<i class='fa-solid fa-play'>");
            if ($(document).width() >= 990) {
                $('#lrc').css("cssText", "display:none !important");
                $('.power').css("cssText", "display:block");
            }
        });

        // 底栏歌词显示
        setInterval(function () {
            const lrcElement = document.querySelector('.aplayer-lrc-current');
            if (lrcElement && lrcElement.textContent.trim()) {
                $("#lrc").html("<span class='lrc-show'><svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' width='18' height='18'><path fill='none' d='M0 0h24v24H0z'/><path d='M12 13.535V3h8v3h-6v11a4 4 0 1 1-2-3.465z' fill='rgba(255,255,255,1)'/></svg>&nbsp;" + lrcElement.textContent + "&nbsp;<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' width='18' height='18'><path fill='none' d='M0 0h24v24H0z'/><path d='M12 13.535V3h8v3h-6v11a4 4 0 1 1-2-3.465z' fill='rgba(255,255,255,1)'/></svg></span>");
            }
        }, 500);
    }
}

// 设置自定义控制按钮
function setupCustomControls() {
    // 一言与音乐切换
    $('#open-music').on('click', function () {
        $('#hitokoto').css("display", "none");
        $('#music').css("display", "flex");
    });

    $("#hitokoto").hover(function () {
        $('#open-music').css("display", "flex");
    }, function () {
        $('#open-music').css("display", "none");
    });

    $('#music-close').on('click', function () {
        $('#music').css("display", "none");
        $('#hitokoto').css("display", "flex");
    });

    // 自定义播放控制
    $('#play').on('click', function () {
        if (globalPlayer) {
            globalPlayer.toggle();
            const music = globalPlayer.audio.name + ' - ' + globalPlayer.audio.artist;
            $("#music-name").html(music);
        }
    });

    $('#last').on('click', function () {
        if (globalPlayer) {
            globalPlayer.skipBack();
            globalPlayer.play();
            const music = globalPlayer.audio.name + ' - ' + globalPlayer.audio.artist;
            $("#music-name").html(music);
        }
    });

    $('#next').on('click', function () {
        if (globalPlayer) {
            globalPlayer.skipForward();
            globalPlayer.play();
            const music = globalPlayer.audio.name + ' - ' + globalPlayer.audio.artist;
            $("#music-name").html(music);
        }
    });

    // 空格键控制播放
    window.addEventListener('keydown', function (e) {
        if (e.keyCode === 32 && globalPlayer) {
            e.preventDefault();
            globalPlayer.toggle();
        }
    });

    // 音量控制
    $("#volume").on('input propertychange touchend', function () {
        const volume = parseFloat($("#volume").val());
        if (globalPlayer) {
            globalPlayer.volume(volume, true);
        }
        
        // 更新音量图标
        if (volume === 0) {
            $("#volume-ico").html("<i class='fa-solid fa-volume-xmark'></i>");
        } else if (volume > 0 && volume <= 0.3) {
            $("#volume-ico").html("<i class='fa-solid fa-volume-off'></i>");
        } else if (volume > 0.3 && volume <= 0.6) {
            $("#volume-ico").html("<i class='fa-solid fa-volume-low'></i>");
        } else {
            $("#volume-ico").html("<i class='fa-solid fa-volume-high'></i>");
        }
    });

    // 音量控制显示/隐藏
    $("#music").hover(function () {
        $('.music-text').css("display", "none");
        $('.music-volume').css("display", "flex");
    }, function () {
        $('.music-text').css("display", "block");
        $('.music-volume').css("display", "none");
    });

    // 打开音乐列表
    $('#music-open').on('click', function () {
        if ($(document).width() >= 990) {
            $('#box').css("display", "block");
            $('#row').css("display", "none");
            $('#more').css("cssText", "display:none !important");
        }
    });
}

// 页面加载完成后初始化播放器
$(document).ready(function() {
    // 请求通知权限
    if (Notification.permission === "default") {
        Notification.requestPermission();
    }
    
    // 延迟初始化播放器，确保页面完全加载
    setTimeout(() => {
        initMusicPlayer();
    }, 1000);
});
