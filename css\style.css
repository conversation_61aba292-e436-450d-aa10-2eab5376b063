/*
作者: imsyy
主页：https://www.imsyy.top/
GitHub：https://github.com/imsyy/home
版权所有，请勿删除
*/

@charset "utf-8";

/* @font-face {
    font-family: "MiSans";
    font-style: normal;
    font-weight: 400;
    font-display: swap;
    src: url('../font/MiSans-Regular.subset.woff2') format('woff2');
} */

@font-face {
    font-family: "Pacifico-Regular";
    src: url('../font/Pacifico-Regular.ttf') format('truetype');
}

@font-face {
    font-family: "UnidreamLED";
    src: url('../font/UnidreamLED.ttf') format('truetype');
}

/*全局样式*/
html,
body {
    width: 100%;
    height: 100%;
    margin: 0;
    padding: 0;
    background-color: #333;
    overflow: hidden;
    font-family: 'HarmonyOS_Regular', sans-serif;
}

*,
a,
p {
    text-decoration: none;
    transition: 0.3s;
    color: #efefef;
    user-select: none;
    cursor: url("data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 8 8' width='10px' height='10px'><circle cx='4' cy='4' r='4' fill='white' /></svg>") 4 4, auto !important;
}


a:hover {
    color: white;
}

.cards {
    transition: 0.5s;
}

.cards:hover {
    transform: scale(1.01);
    transition: 0.5s;
}

.cards:active {
    transform: scale(0.95);
    transition: 0.5s;
}

.noscript {
    z-index: 999999;
    font-size: 0.95rem;
    text-align: center;
    margin: 14px 0px;
}

/*鼠标样式*/
#g-pointer-1 {
    display: none;
}

#g-pointer-2 {
    position: absolute;
    top: 0;
    left: 0;
    width: 18px;
    height: 18px;
    transition: 0.05s linear;
    pointer-events: none;
    background: #ffffff40;
    border-radius: 50%;
    z-index: 9999999;
}

/*背景*/
.bg-all {
    z-index: -1;
    position: absolute;
    top: calc(0px + 0px);
    left: 0;
    width: 100%;
    height: calc(100% - 0px);
    transition: .25s;
}

#bg {
    transform: scale(1.10);
    filter: blur(10px);
    position: fixed;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: opacity 1s, transform .25s, filter .25s;
    backface-visibility: hidden;
}

img.error {
    display: none;
}

.cover {
    opacity: 0;
    position: fixed;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-image: radial-gradient(rgba(0, 0, 0, 0) 0, rgba(0, 0, 0, .5) 100%), radial-gradient(rgba(0, 0, 0, 0) 33%, rgba(0, 0, 0, .3) 166%);
    transition: .25s;
}

/*页面样式*/
section {
    display: block;
    position: fixed;
    width: 100%;
    height: 100%;
    min-height: 600px;
    transform: scale(1.10);
    transition: ease 1.25s;
    opacity: 0;
    filter: blur(10px);
}

main {
    width: 100%;
    height: 100%;
    /*background: rgb(0 0 0 / 20%);*/
    display: flex;
    /*align-items: center;*/
}

.container {
    width: 100%;
    display: flex;
    justify-content: space-around;
}

.row {
    align-items: center;
    justify-content: center;
    width: 100%;
    --bs-gutter-x: 0 !important;
}

.col.\32  {
    margin: 0 1.5rem;
}

.main-left {
    /*transform: translateY(240px);*/
    transform: translateY(40px);
}

.row.rightone {
    display: flex;
    align-items: center;
}

/*头像*/
.main-img {
    display: flex;
    align-items: center;
}

.main-img img {
    border-radius: 50%;
    width: 120px;
}

.main-img img:hover {
    transform: rotate(360deg);
}

.img-title {
    width: 100%;
    margin-left: 12px;
    transform: translateY(-8%);
}

.img-title-big {
    font-size: 5rem;
    font-family: 'Pacifico-Regular' !important;
}

span.img-text {
    font-size: 2rem;
    font-family: 'Pacifico-Regular' !important;
}

/*简介*/
.message {
    background: rgb(0 0 0 / 25%);
    backdrop-filter: blur(10px);
    /*margin: 0.5rem;*/
    padding: 1rem;
    border-radius: 6px;
    margin-top: 3.5rem;
    max-width: 460px;
    cursor: pointer;
}

.des {
    display: flex;
    justify-content: space-between;
}

.des-title {
    margin: 1rem 1rem;
    line-height: 2rem;
    margin-right: auto;
}

span#change {
    font-family: 'Pacifico-Regular' !important;
}

.fa-solid.fa-quote-right {
    align-self: flex-end;
}

/*社交链接*/
.social {
    margin-top: 1rem;
    display: flex;
    align-items: center;
    max-width: 460px;
    height: 42px;
}

.link i {
    font-size: 1.45rem;
    margin: 2px 12px;
}

/*
.social .link i:hover {
    font-size: 2.5rem;
}
*/

#link-text {
    display: none;
    flex: 1;
    text-align: right;
    margin-right: 1rem;
}

/*一言*/
.col.hitokotos {
    margin-right: 0.75rem;
}

.hitokoto {
    width: 100%;
    background: rgb(0 0 0 / 25%);
    backdrop-filter: blur(10px);
    padding: 20px;
    border-radius: 6px;
    height: 165px;
    display: flex;
    justify-content: center;
    flex-direction: column;
}

.hitokoto-all {
    margin-top: 10px;
    display: flex;
    flex-direction: column;
}

.hitokoto-text {
    font-size: 1.10rem;
}

#hitokoto_text {
    word-break: break-all;
    text-overflow: ellipsis;
    overflow: hidden;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
}

.hitokoto-from {
    margin-top: 10px;
    font-weight: bold;
    align-self: flex-end;
    font-size: 1.10rem;
}

.open-music {
    display: none;
    align-items: center;
    justify-content: center;
    background: rgb(0 0 0 / 15%);
    padding: 4px 0px;
    font-size: 0.95rem;
    animation: fade;
    -webkit-animation: fade 0.5s;
    -moz-animation: fade 0.5s;
    -o-animation: fade 0.5s;
    -ms-animation: fade 0.5s;
}

.open-music:hover {
    background: rgb(0 0 0 / 30%);
}

/*音乐播放器卡片*/
#music {
    display: none;
}

.music {
    width: 100%;
    background: rgb(0 0 0 / 25%);
    backdrop-filter: blur(10px);
    padding: 20px;
    border-radius: 6px;
    height: 165px;
    display: flex;
    justify-content: center;
    flex-direction: column;
    animation: fade;
    -webkit-animation: fade 0.3s;
    -moz-animation: fade 0.3s;
    -o-animation: fade 0.3s;
    -ms-animation: fade 0.3s;
}

.music-all {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: space-around;
    height: 100%;
}

.music-button {
    display: flex;
    align-items: center;
    margin-bottom: 6px;
}

.music-control {
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: space-evenly;
    width: 100%;
}

.music-menu {
    height: 26px;
    display: flex;
    align-items: center;
}

.fa-solid.fa-play,
.fa-solid.fa-pause {
    padding: 4px;
    font-size: 2.25rem;
}

#play {
    width: 50px;
    height: 50px;
    display: flex;
    justify-content: center;
    align-items: center;
    border-radius: 6px;
    padding: 6px 10px;
}

#play:hover {
    background: rgb(255 255 255 / 20%);
}

#last,
#next {
    font-size: 1.75rem;
    border-radius: 6px;
    padding: 6px 10px;
}

#last:hover,
#next:hover {
    background: rgb(255 255 255 / 20%);
}

#play:active,
#last:active,
#next:active {
    transform: scale(0.95);
}

.music-text {
    margin-top: 6px;
    font-size: 1.10rem;
    text-overflow: ellipsis;
    max-width: 220px;
    overflow-x: hidden;
    white-space: nowrap;
    animation: fade;
    -webkit-animation: fade 0.5s;
    -moz-animation: fade 0.5s;
    -o-animation: fade 0.5s;
    -ms-animation: fade 0.5s;
}

#music-open,
#music-close {
    background: rgb(255 255 255 / 15%);
    padding: 2px 8px;
    border-radius: 6px;
    margin: 0px 6px;
    text-overflow: ellipsis;
    overflow-x: hidden;
    white-space: nowrap;
}

#music-open:hover,
#music-close:hover {
    background: rgb(255 255 255 / 30%);
}

.music-volume {
    margin-top: 6px;
    display: flex;
    align-items: center;
    flex-direction: row;
    animation: fade;
    -webkit-animation: fade 0.5s;
    -moz-animation: fade 0.5s;
    -o-animation: fade 0.5s;
    -ms-animation: fade 0.5s;
}

#volume-ico {
    padding-top: 2px;
    margin-right: 10px;
}

.music-volume i {
    font-size: 1.25rem;
}

input[type=range] {
    -webkit-appearance: none;
    width: 100%;
    border-radius: 10px;
    height: 8px;
    background: rgb(255 255 255 / 15%);
}

input[type=range]::-webkit-slider-thumb,
input[type=range]::-moz-range-thumb {
    -webkit-appearance: none;
}

input[type=range]::-webkit-slider-runnable-track,
input[type=range]::-moz-range-track {
    height: 15px;
    border-radius: 10px;
}

input[type=range]:focus {
    outline: none;
}

input[type=range]::-webkit-slider-thumb {
    -webkit-appearance: none;
    height: 15px;
    width: 15px;
    background: #ffffff;
    border-radius: 50%;
}

/*时间卡片*/
.col.times {
    margin-left: 0.75rem;
}

.time {
    width: 100%;
    background: rgb(0 0 0 / 25%);
    backdrop-filter: blur(10px);
    padding: 20px;
    border-radius: 6px;
    text-align: center;
    display: flex;
    flex-direction: column;
    height: 165px;
    font-size: 1.10rem;
}

span.time-text {
    font-size: 3.25rem;
    letter-spacing: 2px;
    font-family: 'UnidreamLED' !important;
}

.weather {
    display: flex;
    flex-direction: row;
    justify-content: center;
}

/*分割线*/
.line {
    margin: 1rem 0.25rem;
    margin-top: 2rem;
    font-size: 1.10rem;
    display: flex;
    align-items: center;
}

span.line-text {
    font-size: 1.2rem;
    margin: 0px 6px;
}

i.iconfont.icon-link {
    font-size: 1.2rem;
}

/*链接卡片*/
.link-card {
    height: 100px;
    width: 100%;
    border-radius: 6px;
    background: rgb(0 0 0 / 25%);
    backdrop-filter: blur(10px);
    display: flex;
    align-items: center;
    flex-direction: row;
    justify-content: center;
}

.link-card i {
    margin-left: 0px;
    font-size: 1.65rem;
}

.link-card:hover {
    background: rgb(0 0 0 / 40%);
    transition: 0.5s;
}

span.link-name {
    font-size: 1.1rem;
}

.link-card:hover span.link-name {
    font-size: 1.15rem;
    transition: 0.1s;
}

i.iconfont.icon-a-daohangzhiyindingwei-05,
i.iconfont.icon-z_shangpinheji {
    font-size: 2rem;
}

/*更多页面*/
.more {
    display: none !important;
    width: 46%;
    z-index: 999;
    position: fixed;
    height: 82%;
    right: 4%;
    background: rgb(0 0 0 / 25%);
    backdrop-filter: blur(10px);
    top: 7%;
    border-radius: 6px;
    padding: 30px;
}

.mores .more {
    display: flex !important;
    justify-content: space-evenly;
    flex-direction: column;
    align-items: center;
    animation: fade;
    -webkit-animation: fade 0.3s;
    -moz-animation: fade 0.3s;
    -o-animation: fade 0.3s;
    -ms-animation: fade 0.3s;
}

.mores .col.right {
    display: none;
}

/*关闭按钮*/

.close {
    display: none;
    left: auto;
    top: 4px;
    right: 8px;
    font-size: 1.45rem;
}

.close:hover {
    transform: scale(1.2);
}

/*时间胶囊*/
.progress {
    width: 100%;
    height: 20px;
    align-items: center;
    background: rgb(0 0 0 / 0%) !important;
    backdrop-filter: blur(5px);
}

.progress-bar {
    font-family: 'UnidreamLED' !important;
    background-color: #efefef !important;
    color: rgb(86 77 89) !important;
    font-size: 0.95rem;
    height: 20px;
}

.date {
    width: 100%;
}

.date-text {
    margin: 1rem 0rem 0.5rem 0rem;
}

/*其他链接*/

.mores .link-card {
    height: 48px !important;
}

/*更多页面*/
.box-left {
    flex: 0 44%;
    min-width: 400px;
}

.box {
    position: fixed;
    left: 0;
    right: 0;
    top: 0;
    bottom: 0;
    z-index: 1996;
    background-color: rgba(0, 0, 0, .5);
    backdrop-filter: blur(20px);
    animation: fade 0.3s;
}

.box-wrapper {
    position: absolute;
    top: 50%;
    left: 50%;
    -webkit-transform: translate(-50%, -50%);
    transform: translate(-50%, -50%);
    z-index: 1997;
    width: 80%;
    height: 80%;
    background: rgb(255 255 255 / 40%);
    border-radius: 6px;
    -webkit-animation: fade .3s;
    animation: fade .3s;
    padding: 40px;
    display: flex;
    flex-direction: row;
    justify-content: space-between;
}

#accordion {
    min-width: 360px;
    margin-right: 40px;
    margin-top: 20px;
    margin-left: 14px;
    border-radius: 8px;
    box-shadow: 0 2px 2px 0 rgb(0 0 0 / 7%), 0 1px 5px 0 rgb(0 0 0 / 10%);
}

.accordion-item {
    background-color: transparent !important;
    border: none !important;
}

.accordion-item:first-of-type .accordion-button {
    border-radius: 8px;
}

.accordion-button {
    color: white !important;
    background-color: transparent;
    transition: 0.3s;

}

.accordion-button:focus {
    border-color: #ffffff26 !important;
    outline: none !important;
    box-shadow: none !important;
    border-radius: 8px;
    transition: 0.3s;
}

.accordion-button:not(.collapsed) {
    background-color: #ffffff26;
    border-bottom-left-radius: 0px !important;
    border-bottom-right-radius: 0px !important;
    font-weight: bold;
    transition: 0.3s;
}

.accordion-button::after {
    border-radius: 8px;
    background-image: none !important;
}

.accordion-body {
    padding: 1rem 0rem !important;
    background-color: #ffffff10;
}

.closebox {
    left: auto;
    top: 10px;
    right: 16px;
    font-size: 1.5rem;
}

.closebox:hover {
    transform: scale(1.2);
}

i.iconfont.icon-close,
i.iconfont.icon-github1 {
    font-size: 1.45rem;
}

/*个性设置*/
.set {
    display: flex;
    flex-direction: column;
    padding: 0rem 1.5rem;
}

.btn-group,
.btn-group-vertical {
    margin-top: 10px;
}

.btn-outline-primary {
    color: #eeeeee !important;
    border-color: #ffffff26 !important;
}

.btn-outline-primary:hover {
    background-color: #ffffff26 !important;
    border-color: #eeeeee !important;
}

.btn-check:focus + .btn,
.btn:focus {
    box-shadow: none !important;
}

.btn-check:active + .btn-outline-primary,
.btn-check:checked + .btn-outline-primary,
.btn-outline-primary.active,
.btn-outline-primary.dropdown-toggle.show,
.btn-outline-primary:active {
    background-color: #ffffff26 !important;
    border-color: #eeeeee !important;
}

#wallpaper {
    display: flex;
    align-items: center;
    justify-content: flex-start;
    flex-wrap: wrap;
}

.form-radio {
    flex: 1 1 0%;
    width: 31.3%;
    min-width: 31.3%;
    max-width: 31.3%;
    text-align: center;
    margin: 1%;
}

input[type="radio"] + label {
    padding: 6px 10px;
    background: #ffffff26;
    border-radius: 8px;
    transition: 0.3s;
    border: 2px solid transparent;
    width: 100%;
}

input[type="radio"]:checked + label {
    background: #ffffff06;
    border: 2px solid #eeeeee;
}

/*更新日志*/
.upnote {
    display: flex;
    flex-direction: column;
    padding: 0rem 1.5rem;
    height: 156px;
    overflow-y: auto;
}

.uptext {
    line-height: 32px;
}

/*Aplayer*/
.box-right {
    flex: 0 54%;
    max-width: 54%;
    display: flex;
    flex-direction: row;
    justify-content: center;
    align-items: center;
}

.aplayer {
    background: transparent !important;
    width: 100%;
    border-radius: 6px !important;
    margin-right: 18px;
    font-family: 'HarmonyOS_Regular', sans-serif !important;
}

.aplayer.aplayer-withlrc .aplayer-pic {
    display: none;
}

.aplayer.aplayer-withlrc .aplayer-info {
    margin-left: 0px !important;
}

.aplayer.aplayer-withlrc .aplayer-info {
    background-color: #ffffff26;
    border-radius: 6px;
}

/*歌曲名称*/
.aplayer .aplayer-info .aplayer-music .aplayer-title {
    font-size: 16px !important;
}

/*音乐列表*/
.aplayer.aplayer-withlist .aplayer-list {
    margin-top: 6px;
}

.aplayer .aplayer-list ol li.aplayer-list-light {
    background: #ffffff26 !important;
    border-radius: 6px !important;
}

.aplayer .aplayer-list ol li:hover {
    background: #ffffff26 !important;
    border-radius: 6px !important;
}

.aplayer .aplayer-list ol li {
    border-top: 1px solid transparent !important;
}

.aplayer.aplayer-withlist .aplayer-info {
    border-bottom: 1px solid transparent !important;
}

.aplayer-list-cur {
    background-color: #eeeeee !important;
}

/*控制面板 - Bug*/
.aplayer .aplayer-info .aplayer-controller {
    display: none !important;
}

/*歌词间距*/
.aplayer .aplayer-lrc {
    margin: 4px 0 0px !important;
    height: 34px !important;
}

.aplayer .aplayer-lrc {
    text-align: left !important;
    margin-left: 5px !important;
}

/*歌词大小*/
.aplayer .aplayer-lrc p.aplayer-lrc-current {
    font-size: 14.5px !important;
}

/*全局字体颜色*/
.aplayer .aplayer-info .aplayer-music .aplayer-title,
.aplayer .aplayer-info .aplayer-music .aplayer-author,
.aplayer .aplayer-lrc p,
span.aplayer-list-title,
span.aplayer-list-author,
span.aplayer-list-index {
    color: white !important;
}

/*全局背景*/
.aplayer .aplayer-lrc:after,
.aplayer .aplayer-lrc:before {
    background: transparent !important;
}

/*Aplayer结束*/

/*移动端页面切换按钮*/
i.iconfont.icon-bars,
i.iconfont.icon-times {
    font-size: 1.25rem;
}

/*页脚样式*/
footer {
    text-align: center;
    height: 46px;
    backdrop-filter: blur(10px);
    background: rgb(0 0 0 / 25%);
}

.power {
    line-height: 46px;
    color: #eeeeee;
    animation: fade;
    -webkit-animation: fade 0.75s;
    -moz-animation: fade 0.75s;
    -o-animation: fade 0.75s;
    -ms-animation: fade 0.75s;
}

/*播放音乐时底栏歌词*/
#lrc {
    display: none;
    line-height: 46px;
    color: #eeeeee;
    animation: fade;
    -webkit-animation: fade 0.75s;
    -moz-animation: fade 0.75s;
    -o-animation: fade 0.75s;
    -ms-animation: fade 0.75s;
}

.lrc-show {
    display: flex;
    justify-content: center;
    align-items: center;
}

/*弹窗样式*/
.iziToast {
    backdrop-filter: blur(10px) !important;
}

.iziToast > .iziToast-body .iziToast-title {
    font-size: 16px !important;
}

.iziToast-body .iziToast-message {
    line-height: 18px !important;
}

.iziToast > .iziToast-body .iziToast-message {
    margin: 6px 0px 4px 0px !important;
}

.iziToast:after {
    box-shadow: none !important;
}

.iziToast > .iziToast-body .iziToast-texts {
    margin: 6px 6px !important;
    display: flex !important;
    align-items: center;
}

.iziToast > .iziToast-body i {
    margin-left: 6px;
    margin-top: 10px;
}

.iziToast-message {
    word-break: break-all !important;
}

.iziToast > .iziToast-close {
    background: url(https://cdn.jsdelivr.net/gh/imsyy/file/pic/close.png) no-repeat 50% 50% !important;
    background-size: 8px !important;
}

/*滚动条样式*/
::-webkit-scrollbar {
    width: 6px;
    height: 6px;
    background-color: transparent;
}

::-webkit-scrollbar-thumb {
    border-radius: 10px;
    background-color: #eeeeee;
}
