/*
性能优化模块
优化资源加载，提高页面性能

作者: acmakb
*/

const Performance = {
    // 初始化性能优化
    init() {
        this.optimizeImages();
        this.setupLazyLoading();
        this.optimizeAnimations();
        this.setupResourceHints();
        this.monitorPerformance();
    },

    // 优化图片加载
    optimizeImages() {
        // 背景图片懒加载
        const bgImages = [
            './img/background1.webp',
            './img/background2.webp',
            './img/background3.webp',
            './img/background4.webp',
            './img/background5.webp'
        ];

        // 随机选择背景图片
        const randomBg = bgImages[Math.floor(Math.random() * bgImages.length)];
        
        // 预加载背景图片
        const img = new Image();
        img.onload = () => {
            const bgElement = document.getElementById('bg');
            if (bgElement) {
                bgElement.src = randomBg;
                bgElement.classList.add('loaded');
            }
        };
        img.onerror = () => {
            // 使用纯色背景作为备用
            const bgElement = document.getElementById('bg');
            if (bgElement) {
                bgElement.style.background = 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)';
                bgElement.classList.add('loaded');
            }
        };
        img.src = randomBg;
    },

    // 设置懒加载
    setupLazyLoading() {
        // 使用Intersection Observer进行懒加载
        if ('IntersectionObserver' in window) {
            const lazyElements = document.querySelectorAll('[data-lazy]');
            const lazyObserver = new IntersectionObserver((entries) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        const element = entry.target;
                        const src = element.dataset.lazy;
                        if (src) {
                            element.src = src;
                            element.removeAttribute('data-lazy');
                            lazyObserver.unobserve(element);
                        }
                    }
                });
            });

            lazyElements.forEach(element => {
                lazyObserver.observe(element);
            });
        }
    },

    // 优化动画性能
    optimizeAnimations() {
        // 检测用户是否偏好减少动画
        if (window.matchMedia('(prefers-reduced-motion: reduce)').matches) {
            document.documentElement.style.setProperty('--animation-duration', '0.01s');
            return;
        }

        // 使用requestAnimationFrame优化动画
        this.setupRAFAnimations();
        
        // 暂停不可见元素的动画
        this.pauseInvisibleAnimations();
    },

    // 设置RAF动画
    setupRAFAnimations() {
        let ticking = false;
        
        const updateAnimations = () => {
            // 更新鼠标跟随动画
            this.updateMouseFollower();
            ticking = false;
        };

        const requestTick = () => {
            if (!ticking) {
                requestAnimationFrame(updateAnimations);
                ticking = true;
            }
        };

        // 节流鼠标移动事件
        document.addEventListener('mousemove', requestTick);
    },

    // 更新鼠标跟随器
    updateMouseFollower() {
        const pointer = document.getElementById('g-pointer-2');
        if (pointer && this.mouseX !== undefined && this.mouseY !== undefined) {
            pointer.style.transform = `translate(${this.mouseX}px, ${this.mouseY}px)`;
        }
    },

    // 暂停不可见动画
    pauseInvisibleAnimations() {
        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                const element = entry.target;
                if (entry.isIntersecting) {
                    element.style.animationPlayState = 'running';
                } else {
                    element.style.animationPlayState = 'paused';
                }
            });
        });

        // 观察所有有动画的元素
        document.querySelectorAll('[class*="animate"], .loader, .spinner').forEach(el => {
            observer.observe(el);
        });
    },

    // 设置资源提示
    setupResourceHints() {
        // DNS预解析
        const domains = [
            'cdn.jsdelivr.net',
            's1.hdslb.com',
            'v1.hitokoto.cn',
            'restapi.amap.com'
        ];

        domains.forEach(domain => {
            const link = document.createElement('link');
            link.rel = 'dns-prefetch';
            link.href = `//${domain}`;
            document.head.appendChild(link);
        });

        // 预连接关键资源
        const preconnectDomains = ['cdn.jsdelivr.net'];
        preconnectDomains.forEach(domain => {
            const link = document.createElement('link');
            link.rel = 'preconnect';
            link.href = `https://${domain}`;
            link.crossOrigin = 'anonymous';
            document.head.appendChild(link);
        });
    },

    // 性能监控
    monitorPerformance() {
        // 监控关键性能指标
        if ('PerformanceObserver' in window) {
            // 监控LCP (Largest Contentful Paint)
            const lcpObserver = new PerformanceObserver((list) => {
                const entries = list.getEntries();
                const lastEntry = entries[entries.length - 1];
                console.log('LCP:', lastEntry.startTime);
            });
            lcpObserver.observe({ entryTypes: ['largest-contentful-paint'] });

            // 监控FID (First Input Delay)
            const fidObserver = new PerformanceObserver((list) => {
                const entries = list.getEntries();
                entries.forEach(entry => {
                    console.log('FID:', entry.processingStart - entry.startTime);
                });
            });
            fidObserver.observe({ entryTypes: ['first-input'] });
        }

        // 页面加载完成时间
        window.addEventListener('load', () => {
            setTimeout(() => {
                const perfData = performance.timing;
                const loadTime = perfData.loadEventEnd - perfData.navigationStart;
                console.log('Page Load Time:', loadTime + 'ms');
                
                // 如果加载时间过长，显示提示
                if (loadTime > 5000) {
                    iziToast.warning({
                        timeout: 5000,
                        message: '页面加载较慢，建议检查网络连接'
                    });
                }
            }, 0);
        });
    },

    // 内存优化
    optimizeMemory() {
        // 定期清理DOM
        this.setupDOMCleanup();
    },

    // 清理事件监听器
    cleanupEventListeners() {
        // 音乐控制事件已移至music-new.js处理
        // 这里只保留其他必要的事件清理
    },

    // 设置DOM清理
    setupDOMCleanup() {
        // 定期清理不需要的DOM元素
        setInterval(() => {
            // 清理已完成的通知
            const notifications = document.querySelectorAll('.iziToast[style*="display: none"]');
            notifications.forEach(notification => {
                if (notification.parentNode) {
                    notification.parentNode.removeChild(notification);
                }
            });
        }, 30000);
    },

    // 网络优化
    optimizeNetwork() {
        // 使用Service Worker缓存资源
        if ('serviceWorker' in navigator) {
            navigator.serviceWorker.register('./sw.js').catch(err => {
                console.log('Service Worker registration failed:', err);
            });
        }

        // 预加载下一页资源
        this.preloadNextPageResources();
    },

    // 预加载下一页资源
    preloadNextPageResources() {
        const links = document.querySelectorAll('a[href^="http"]');
        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    const link = entry.target;
                    const prefetchLink = document.createElement('link');
                    prefetchLink.rel = 'prefetch';
                    prefetchLink.href = link.href;
                    document.head.appendChild(prefetchLink);
                    observer.unobserve(link);
                }
            });
        });

        links.forEach(link => observer.observe(link));
    }
};

// 初始化性能优化
document.addEventListener('DOMContentLoaded', () => {
    Performance.init();
});

// 页面加载完成后进行进一步优化
window.addEventListener('load', () => {
    setTimeout(() => {
        Performance.optimizeMemory();
        Performance.optimizeNetwork();
    }, 1000);
});

// 导出供其他模块使用
window.Performance = Performance;
