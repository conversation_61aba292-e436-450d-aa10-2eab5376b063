/*
布局修复模块
防止CDN加载导致的布局错位问题

作者: acmakb
*/

// 布局修复工具
const LayoutFix = {
    // 初始化
    init() {
        this.preventFOUC();
        this.setupResourceMonitoring();
        this.setupFallbacks();
        this.optimizeLoading();
    },

    // 防止FOUC (Flash of Unstyled Content)
    preventFOUC() {
        // 添加防止FOUC的类
        document.documentElement.classList.add('loading-prevent-fouc');
        
        // 页面加载完成后移除
        window.addEventListener('load', () => {
            setTimeout(() => {
                document.documentElement.classList.remove('loading-prevent-fouc');
                document.documentElement.classList.add('loaded');
            }, 100);
        });
    },

    // 监控资源加载
    setupResourceMonitoring() {
        const criticalResources = [
            'https://s1.hdslb.com/bfs/static/jinkela/long/font/regular.css',
            'https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css',
            'https://cdn.jsdelivr.net/npm/@fortawesome/fontawesome-free@6.2.0/css/all.min.css'
        ];

        let loadedCount = 0;
        const totalCount = criticalResources.length;

        criticalResources.forEach(url => {
            this.checkResourceLoad(url, () => {
                loadedCount++;
                this.updateLoadingProgress(loadedCount / totalCount);
            });
        });
    },

    // 检查资源加载状态
    checkResourceLoad(url, callback) {
        const link = document.querySelector(`link[href="${url}"]`);
        if (link) {
            if (link.sheet) {
                callback();
            } else {
                link.addEventListener('load', callback);
                link.addEventListener('error', () => {
                    console.warn(`Failed to load resource: ${url}`);
                    callback(); // 即使失败也继续
                });
            }
        }
    },

    // 更新加载进度
    updateLoadingProgress(progress) {
        const loadingText = document.getElementById('loading-text');
        if (loadingText) {
            const percentage = Math.round(progress * 100);
            loadingText.textContent = `加载中... ${percentage}%`;
        }
    },

    // 设置备用方案
    setupFallbacks() {
        // 字体备用方案
        this.setupFontFallback();
        
        // 图标备用方案
        this.setupIconFallback();
        
        // Bootstrap备用方案
        this.setupBootstrapFallback();
    },

    // 字体备用方案
    setupFontFallback() {
        const fontLink = document.querySelector('link[href*="hdslb.com"]');
        if (fontLink) {
            fontLink.addEventListener('error', () => {
                // 使用系统字体作为备用
                const fallbackStyle = document.createElement('style');
                fallbackStyle.textContent = `
                    body, * {
                        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif !important;
                    }
                `;
                document.head.appendChild(fallbackStyle);
            });
        }
    },

    // 图标备用方案
    setupIconFallback() {
        setTimeout(() => {
            const icons = document.querySelectorAll('i[class*="fa-"]');
            icons.forEach(icon => {
                if (getComputedStyle(icon).fontFamily.indexOf('Font Awesome') === -1) {
                    // FontAwesome未加载，使用文字替代
                    const iconClass = icon.className;
                    let replacement = '●';
                    
                    if (iconClass.includes('fa-play')) replacement = '▶';
                    else if (iconClass.includes('fa-pause')) replacement = '⏸';
                    else if (iconClass.includes('fa-music')) replacement = '♪';
                    else if (iconClass.includes('fa-github')) replacement = 'GH';
                    else if (iconClass.includes('fa-envelope')) replacement = '@';
                    
                    icon.textContent = replacement;
                    icon.style.fontFamily = 'monospace';
                }
            });
        }, 3000);
    },

    // Bootstrap备用方案
    setupBootstrapFallback() {
        setTimeout(() => {
            // 检查Bootstrap是否加载
            const testElement = document.createElement('div');
            testElement.className = 'container';
            testElement.style.visibility = 'hidden';
            document.body.appendChild(testElement);
            
            const hasBootstrap = getComputedStyle(testElement).maxWidth !== 'none';
            document.body.removeChild(testElement);
            
            if (!hasBootstrap) {
                // 添加基础网格样式
                const fallbackStyle = document.createElement('style');
                fallbackStyle.textContent = `
                    .container { max-width: 1200px; margin: 0 auto; padding: 0 15px; }
                    .row { display: flex; flex-wrap: wrap; margin: 0 -15px; }
                    .col { flex: 1; padding: 0 15px; }
                    @media (max-width: 768px) {
                        .col { flex: 100%; }
                    }
                `;
                document.head.appendChild(fallbackStyle);
            }
        }, 2000);
    },

    // 优化加载
    optimizeLoading() {
        // 预加载关键资源
        this.preloadCriticalResources();
        
        // 延迟加载非关键资源
        this.deferNonCriticalResources();
    },

    // 预加载关键资源
    preloadCriticalResources() {
        const criticalResources = [
            './css/style.css',
            './js/main.js'
        ];

        criticalResources.forEach(url => {
            const link = document.createElement('link');
            link.rel = 'preload';
            link.as = url.endsWith('.css') ? 'style' : 'script';
            link.href = url;
            document.head.appendChild(link);
        });
    },

    // 延迟加载非关键资源
    deferNonCriticalResources() {
        // 延迟加载动画CSS
        setTimeout(() => {
            const animationLink = document.querySelector('link[href*="animation.css"]');
            if (animationLink && !animationLink.sheet) {
                animationLink.media = 'all';
            }
        }, 1000);
    },

    // 修复常见布局问题
    fixCommonIssues() {
        // 修复高度问题
        this.fixHeightIssues();
        
        // 修复定位问题
        this.fixPositionIssues();
        
        // 修复响应式问题
        this.fixResponsiveIssues();
    },

    // 修复高度问题
    fixHeightIssues() {
        const section = document.getElementById('section');
        if (section) {
            section.style.minHeight = '100vh';
        }
    },

    // 修复定位问题
    fixPositionIssues() {
        const main = document.getElementById('main');
        if (main) {
            main.style.position = 'relative';
            main.style.zIndex = '1';
        }
    },

    // 修复响应式问题
    fixResponsiveIssues() {
        const viewport = document.querySelector('meta[name="viewport"]');
        if (!viewport) {
            const meta = document.createElement('meta');
            meta.name = 'viewport';
            meta.content = 'width=device-width, initial-scale=1, shrink-to-fit=no';
            document.head.appendChild(meta);
        }
    }
};

// 立即初始化
document.addEventListener('DOMContentLoaded', () => {
    LayoutFix.init();
});

// 页面加载完成后进行修复
window.addEventListener('load', () => {
    setTimeout(() => {
        LayoutFix.fixCommonIssues();
    }, 500);
});

// 导出供其他模块使用
window.LayoutFix = LayoutFix;
