var _hmt=_hmt||[],er3eport={};er3eport.start=(new Date).getTime(),er3eport.codetime=function(){return(new Date).getTime()-er3eport.start},er3eport.dodnt=function(){window.dnt=1,"function"==typeof window.dodnt&&window.dodnt()},er3eport.jqdefined=function(){_hmt.push(["_setCustomVar",2,"JSERROR","jQueryDefined # "+er3eport.codetime(),3]),er3eport.dodnt()},er3eport.listener=function(e){var t,r=!1,n="JSERROR";"string"==typeof e?r=e:(e.error?r=e.error.stack:e.message&&(r=e.message),e.srcElement&&e.srcElement.src?t=e.srcElement.src:e.target&&e.target.src&&(t=e.target.src),r||(t?(r="FileError: "+t,n="FILEERROR"):e.type&&(r=e.type)));return r="string"!=typeof r?"empty_error_string":r.replace(/\n/g,"").replace(/\s+/g," "),_hmt.push(["_trackEvent",n,window.location.pathname,r+" #UA# "+navigator.userAgent+" # "+er3eport.codetime()]),!0},"function"==typeof jQuery&&er3eport.jqdefined(),Object.defineProperty&&Object.defineProperty(window,"jQuery",{set:er3eport.jqdefined}),window.addEventListener?window.addEventListener("error",er3eport.listener,!0):window.attachEvent&&window.attachEvent("onerror",er3eport.listener);