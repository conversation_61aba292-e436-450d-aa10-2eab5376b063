/*
关键CSS - 优先加载，防止布局错位
作者: acmakb
*/

/* 基础重置和布局 */
html, body {
    width: 100%;
    height: 100%;
    margin: 0;
    padding: 0;
    background-color: #333;
    overflow: hidden;
    font-family: 'HarmonyOS_Regular', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

/* 防止FOUC (Flash of Unstyled Content) */
.loading-prevent-fouc {
    visibility: hidden;
}

.loaded .loading-prevent-fouc {
    visibility: visible;
}

/* 加载动画 - 关键样式 */
#loading-box {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: #333;
    z-index: 9999;
    display: flex;
    justify-content: center;
    align-items: center;
    transition: all 1s ease;
}

#loading-box.loaded {
    opacity: 0;
    visibility: hidden;
    pointer-events: none;
}

.spinner-box {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
}

.loader {
    width: 60px;
    height: 60px;
    position: relative;
    margin-bottom: 20px;
}

.inner {
    position: absolute;
    border: 3px solid transparent;
    border-top-color: #fff;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

.inner.one {
    width: 60px;
    height: 60px;
}

.inner.two {
    width: 40px;
    height: 40px;
    top: 10px;
    left: 10px;
    animation-delay: 0.1s;
}

.inner.three {
    width: 20px;
    height: 20px;
    top: 20px;
    left: 20px;
    animation-delay: 0.2s;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.loading-word {
    text-align: center;
    color: #fff;
}

.loading-title {
    font-size: 1.5rem;
    margin: 0 0 10px 0;
    font-weight: 600;
}

#loading-text {
    font-size: 0.9rem;
    opacity: 0.8;
}

/* 主要布局容器 */
.section {
    width: 100%;
    height: 100vh;
    position: relative;
    transform: scale(1.1);
    opacity: 0;
    filter: blur(5px);
    transition: all 1.5s ease;
}

.section.loaded {
    transform: scale(1);
    opacity: 1;
    filter: blur(0);
}

/* 背景图片容器 */
.bg-all {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: -1;
}

#bg {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transform: scale(1.1);
    filter: blur(3px);
    transition: all 1.5s ease;
}

#bg.loaded {
    transform: scale(1);
    filter: blur(0);
}

.cover {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.3);
    opacity: 0;
    transition: opacity 1.5s ease;
}

.cover.loaded {
    opacity: 1;
}

/* 主要内容区域 */
.main {
    width: 100%;
    height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 20px;
    box-sizing: border-box;
}

.container {
    width: 100%;
    max-width: 1200px;
    margin: 0 auto;
}

/* 基础卡片样式 */
.cards {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    border-radius: 12px;
    border: 1px solid rgba(255, 255, 255, 0.2);
    transition: all 0.3s ease;
}

.cards:hover {
    transform: translateY(-2px);
    background: rgba(255, 255, 255, 0.15);
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
}

/* 基础文字样式 */
* {
    color: #fff;
    text-decoration: none;
    user-select: none;
    transition: all 0.3s ease;
}

a:hover {
    color: #fff;
    opacity: 0.8;
}

/* 响应式基础 */
@media (max-width: 768px) {
    .main {
        padding: 10px;
    }
    
    .loading-title {
        font-size: 1.2rem;
    }
    
    #loading-text {
        font-size: 0.8rem;
    }
}

/* 防止布局跳动的占位符 */
.placeholder-height {
    min-height: 200px;
}

/* 图标字体加载前的占位 */
.icon-placeholder {
    display: inline-block;
    width: 1em;
    height: 1em;
    background: rgba(255, 255, 255, 0.3);
    border-radius: 2px;
}

/* 确保关键元素立即可见 */
.critical-visible {
    opacity: 1 !important;
    visibility: visible !important;
}
