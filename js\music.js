/*
音乐播放器模块
重构版本 - 使用本地音乐列表，避免API依赖问题

作者: acmakb
基于原版本修改优化
*/

// 本地音乐列表配置
const musicList = [
    {
        name: '夜曲',
        artist: '周杰伦',
        url: 'https://music.163.com/song/media/outer/url?id=186016.mp3',
        cover: 'https://p2.music.126.net/g3Yk9K_YwsOSkyBSHy7XQQ==/109951165050166241.jpg',
        lrc: '[00:00.00] 夜曲 - 周杰伦\n[00:29.00]一群嗜血的蚂蚁被腐肉所吸引\n[00:32.00]我面无表情看孤独的风景\n[00:35.00]失去你爱哭的人是我\n[00:39.00]牵着你手却哭红了眼睛'
    },
    {
        name: '稻香',
        artist: '周杰伦',
        url: 'https://music.163.com/song/media/outer/url?id=186431.mp3',
        cover: 'https://p1.music.126.net/6y-UleORITEDbvrOLV0Q8A==/5639395138885805.jpg',
        lrc: '[00:00.00] 稻香 - 周杰伦\n[00:03.00]对这个世界如果你有太多的抱怨\n[00:06.00]跌倒了就不敢继续往前走\n[00:09.00]为什么人要这么的脆弱 堕落'
    },
    {
        name: '青花瓷',
        artist: '周杰伦',
        url: 'https://music.163.com/song/media/outer/url?id=186446.mp3',
        cover: 'https://p1.music.126.net/Md3RLH0fe2a_3dMDnfqoQg==/18590542604286213.jpg',
        lrc: '[00:00.00] 青花瓷 - 周杰伦\n[00:20.00]素胚勾勒出青花笔锋浓转淡\n[00:25.00]瓶身描绘的牡丹一如你初妆\n[00:30.00]冉冉檀香透过窗心事我了然'
    }
];

// 初始化音乐播放器
function initMusicPlayer() {
    try {
        const ap = new APlayer({
            container: document.getElementById('aplayer'),
            order: 'random',
            preload: 'auto',
            listMaxHeight: '336px',
            volume: 0.5,
            mutex: true,
            lrcType: 3,
            audio: musicList,
        });

        /* 底栏歌词 */
        setInterval(function () {
            $("#lrc").html("<span class='lrc-show'><svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' width='18' height='18'><path fill='none' d='M0 0h24v24H0z'/><path d='M12 13.535V3h8v3h-6v11a4 4 0 1 1-2-3.465z' fill='rgba(255,255,255,1)'/></svg>&nbsp;" + $(".aplayer-lrc-current").text() + "&nbsp;<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' width='18' height='18'><path fill='none' d='M0 0h24v24H0z'/><path d='M12 13.535V3h8v3h-6v11a4 4 0 1 1-2-3.465z' fill='rgba(255,255,255,1)'/></svg></span>");
        }, 500);

        /* 音乐通知及控制 */
        ap.on('play', function () {
            music = $(".aplayer-title").text() + $(".aplayer-author").text();
            iziToast.info({
                timeout: 4000,
                icon: "fa-solid fa-circle-play",
                displayMode: 'replace',
                message: music
            });
            $("#play").html("<i class='fa-solid fa-pause'>");
            $("#music-name").html($(".aplayer-title").text() + $(".aplayer-author").text());
            if ($(document).width() >= 990) {
                $('.power').css("cssText", "display:none");
                $('#lrc').css("cssText", "display:block !important");
            };
            Notification.requestPermission().then(res => {
                console.log(res)
            });
            new Notification('音乐通知', {
                body: '正在播放：' + music,
                tag: 1
            });
        });

        ap.on('pause', function () {
            $("#play").html("<i class='fa-solid fa-play'>");
            if ($(document).width() >= 990) {
                $('#lrc').css("cssText", "display:none !important");
                $('.power').css("cssText", "display:block");
            }
        });

        $("#music").hover(function () {
            $('.music-text').css("display", "none");
            $('.music-volume').css("display", "flex");
        }, function () {
            $('.music-text').css("display", "block");
            $('.music-volume').css("display", "none");
        })

        /* 一言与音乐切换 */
        $('#open-music').on('click', function () {
            $('#hitokoto').css("display", "none");
            $('#music').css("display", "flex");
        });

        $("#hitokoto").hover(function () {
            $('#open-music').css("display", "flex");
        }, function () {
            $('#open-music').css("display", "none");
        })

        $('#music-close').on('click', function () {
            $('#music').css("display", "none");
            $('#hitokoto').css("display", "flex");
        });

        /* 上下曲 */
        $('#play').on('click', function () {
            ap.toggle();
            $("#music-name").html($(".aplayer-title").text() + $(".aplayer-author").text());
        });

        $('#last').on('click', function () {
            ap.skipBack();
            ap.play();
            $("#music-name").html($(".aplayer-title").text() + $(".aplayer-author").text());
        });

        $('#next').on('click', function () {
            ap.skipForward();
            ap.play();
            $("#music-name").html($(".aplayer-title").text() + $(".aplayer-author").text());
        });

        window.onkeydown = function (e) {
            if (e.keyCode == 32) {
                ap.toggle();
            }
        }

        /* 打开音乐列表 */
        $('#music-open').on('click', function () {
            if ($(document).width() >= 990) {
                $('#box').css("display", "block");
                $('#row').css("display", "none");
                $('#more').css("cssText", "display:none !important");
            }
        });

        //音量调节
        $("#volume").on('input propertychange touchend', function () {
            let x = $("#volume").val();
            ap.volume(x, true);
            if (x == 0) {
                $("#volume-ico").html("<i class='fa-solid fa-volume-xmark'></i>");
            } else if (x > 0 && x <= 0.3) {
                $("#volume-ico").html("<i class='fa-solid fa-volume-off'></i>");
            } else if (x > 0.3 && x <= 0.6) {
                $("#volume-ico").html("<i class='fa-solid fa-volume-low'></i>");
            } else {
                $("#volume-ico").html("<i class='fa-solid fa-volume-high'></i>");
            }
        });

        // 播放器初始化成功提示
        iziToast.success({
            timeout: 3000,
            icon: "fa-solid fa-music",
            displayMode: 'replace',
            message: '音乐播放器加载成功'
        });

    } catch (error) {
        console.error('音乐播放器初始化失败:', error);
        setTimeout(function () {
            iziToast.error({
                timeout: 8000,
                icon: "fa-solid fa-circle-exclamation",
                displayMode: 'replace',
                message: '音乐播放器加载失败'
            });
        }, 3800);
    }
}

// 页面加载完成后初始化播放器
$(document).ready(function() {
    initMusicPlayer();
});
