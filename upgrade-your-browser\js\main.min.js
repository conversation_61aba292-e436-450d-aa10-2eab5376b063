var l=window.location,s=l.search.substr(1),r="",dr=document.referrer,r1=s.match(/(^|&)referrer=((http|https)[^&]*)/),r2=s.match(/^referrer=((http|https)\:\/\/.*)$/);null!==r2?r=r2[1]:null!==r1&&(r=r1[2]),""===r&&""!==dr&&null===dr.match(/\/(?:.*\.dmeng\.net|(?:(?:(?:www|m|wap|cn|search)\.)?(?:google|baidu|sogou|bing|so|yahoo|yandex|duckduckgo)\.com(?:\.[a-z]+)?))/)&&(l.href=l.protocol+"//"+l.hostname+l.pathname+"?referrer="+encodeURIComponent(dr)+"&"+s);var url=decodeURIComponent(r);-1!=url.indexOf(window.location.hostname+"/")&&(url="");var targetUrlHTML="";if(""!==url){var m=url.match(/^(https?\:\/\/[^/]+\/)index\.html$/);targetUrlHTML='&nbsp;<a href="'+url+'" onclick="alert(\'点击右键“复制快捷方式”（复制链接）到更先进的浏览器访问。\');return false;" rel="noreferrer">'+(m?m[1]:url)+"</a>&nbsp;"}