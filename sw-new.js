/*
Service Worker - 缓存优化 (新版本)
提高页面加载速度和离线体验

作者: acmakb
版本: 2.0
*/

const CACHE_NAME = 'personal-blog-v2.0';
const CACHE_URLS = [
    './',
    './index.html',
    './css/critical.css',
    './css/style.css',
    './css/mobile.css',
    './css/loading.css',
    './css/animation.css',
    './js/layout-fix.js',
    './js/performance.js',
    './js/main.js',
    './js/set.js',
    './js/time.js',
    './js/music-new.js',
    './js/js.cookie.js',
    './img/icon/favicon.png',
    './img/icon/apple-touch-icon.png'
];

// 安装事件 - 缓存资源
self.addEventListener('install', event => {
    console.log('Service Worker 安装中...');
    event.waitUntil(
        caches.open(CACHE_NAME)
            .then(cache => {
                console.log('缓存已打开');
                return cache.addAll(CACHE_URLS);
            })
            .then(() => {
                console.log('资源缓存完成');
                return self.skipWaiting();
            })
            .catch(error => {
                console.error('缓存失败:', error);
            })
    );
});

// 激活事件 - 清理旧缓存
self.addEventListener('activate', event => {
    console.log('Service Worker 激活中...');
    event.waitUntil(
        caches.keys().then(cacheNames => {
            return Promise.all(
                cacheNames.map(cacheName => {
                    if (cacheName !== CACHE_NAME) {
                        console.log('删除旧缓存:', cacheName);
                        return caches.delete(cacheName);
                    }
                })
            );
        }).then(() => {
            console.log('Service Worker 激活完成');
            return self.clients.claim();
        })
    );
});

// 拦截请求 - 缓存策略
self.addEventListener('fetch', event => {
    const request = event.request;
    const url = new URL(request.url);

    // 只处理同源请求和特定的外部资源
    if (url.origin === location.origin || shouldCacheExternal(url)) {
        event.respondWith(
            caches.match(request)
                .then(response => {
                    // 缓存命中，返回缓存的资源
                    if (response) {
                        console.log('从缓存加载:', request.url);
                        return response;
                    }

                    // 缓存未命中，从网络获取
                    return fetch(request)
                        .then(response => {
                            // 检查响应是否有效
                            if (!response || response.status !== 200 || response.type !== 'basic') {
                                return response;
                            }

                            // 克隆响应，因为响应流只能使用一次
                            const responseToCache = response.clone();

                            // 将响应添加到缓存
                            caches.open(CACHE_NAME)
                                .then(cache => {
                                    cache.put(request, responseToCache);
                                    console.log('已缓存:', request.url);
                                });

                            return response;
                        })
                        .catch(error => {
                            console.error('网络请求失败:', error);
                            
                            // 如果是HTML请求且网络失败，返回离线页面
                            if (request.destination === 'document') {
                                return caches.match('./index.html');
                            }
                            
                            // 其他资源返回空响应
                            return new Response('', {
                                status: 404,
                                statusText: 'Not Found'
                            });
                        });
                })
        );
    }
});

// 判断是否应该缓存外部资源
function shouldCacheExternal(url) {
    const allowedDomains = [
        'cdn.jsdelivr.net',
        's1.hdslb.com'
    ];
    
    return allowedDomains.some(domain => url.hostname.includes(domain));
}

// 消息处理 - 与主线程通信
self.addEventListener('message', event => {
    const { type, data } = event.data;
    
    switch (type) {
        case 'SKIP_WAITING':
            self.skipWaiting();
            break;
            
        case 'GET_CACHE_SIZE':
            getCacheSize().then(size => {
                event.ports[0].postMessage({ type: 'CACHE_SIZE', size });
            });
            break;
            
        case 'CLEAR_CACHE':
            clearCache().then(success => {
                event.ports[0].postMessage({ type: 'CACHE_CLEARED', success });
            });
            break;
            
        default:
            console.log('未知消息类型:', type);
    }
});

// 获取缓存大小
async function getCacheSize() {
    try {
        const cache = await caches.open(CACHE_NAME);
        const requests = await cache.keys();
        let totalSize = 0;
        
        for (const request of requests) {
            const response = await cache.match(request);
            if (response) {
                const blob = await response.blob();
                totalSize += blob.size;
            }
        }
        
        return totalSize;
    } catch (error) {
        console.error('获取缓存大小失败:', error);
        return 0;
    }
}

// 清理缓存
async function clearCache() {
    try {
        const cacheNames = await caches.keys();
        await Promise.all(
            cacheNames.map(cacheName => caches.delete(cacheName))
        );
        console.log('所有缓存已清理');
        return true;
    } catch (error) {
        console.error('清理缓存失败:', error);
        return false;
    }
}

console.log('Service Worker 已加载');
